import { buildDocumentAwarenessCmdOption, ErrorHandlerDecorator } from '@viclass/editor.core';
import { syncRenderCommands } from '../cmd';
import { geoDefaultHandlerFn } from '../error-handler';
import { GeometryEditor } from '../geo.editor';
import { GeometryToolBar } from '../geo.toolbar';
import {
    CommonToolState,
    ConstructionRequest,
    GeoElConstructionRequest,
    RenderAngle,
    RenderLine,
    RenderVertex,
} from '../model';
import { GeometryToolType, GeoPointerEvent } from '../model/geo.models';
import { PreviewQueue } from '../model/preview.util';
import { GeoDocCtrl } from '../objects';
import { or, stroke, then, ThenSelector, vertex, vertexOnStroke } from '../selectors';
import { constructExec, GeometryTool } from './geo.tool';
import { NamingElementTool } from './naming.element.tool';
import {
    addHistoryItemFromConstructionResponse,
    buildPointConstruction,
    getFocusDocCtrl,
    handleIfPointerNotInError,
    pickPointName,
    requestElementNames,
} from './tool.utils';

/**
 * Bisector Line Tool - Creates bisector lines from angles using selector DSL
 * <AUTHOR>
 */
export class CreateBisectorLineTool extends GeometryTool<CommonToolState> {
    readonly toolType: GeometryToolType = 'CreateBisectorLineTool';

    private pQ = new PreviewQueue();
    private selectedAngle: RenderAngle | undefined;
    private bisectorVector: number[] | undefined;
    private selectionStep: 'angle' | 'endpoint' = 'angle';
    private endSelector: ThenSelector | undefined;

    constructor(editor: GeometryEditor, toolbar: GeometryToolBar) {
        super(editor, toolbar);
        this.doRegisterPointer();
    }

    override resetState() {
        this.endSelector?.reset();
        this.selectedAngle = undefined;
        this.bisectorVector = undefined;
        this.selectionStep = 'angle';
        super.resetState();
    }

    /**
     * Calculate bisector vector from two angle vectors
     */
    private calculateBiSectorVectorOfAngle(a: number[], b: number[]): number[] {
        // Calculate the dot product of a and b
        const dotProduct = a[0] * b[0] + a[1] * b[1];

        // Magnitude of vector a
        const magnitudeA = Math.sqrt(a[0] * a[0] + a[1] * a[1]);

        // Magnitude of vector b
        const magnitudeB = Math.sqrt(b[0] * b[0] + b[1] * b[1]);

        // Calculate the cosine of the angle between a and b
        const cosTheta = dotProduct / (magnitudeA * magnitudeB);

        // Calculate the sine of the angle between a and b
        const sinTheta = Math.sqrt(1 - cosTheta * cosTheta);

        // Calculate the x and y components of the angle bisector vector
        const cX = (magnitudeA * b[1] - magnitudeB * a[1]) / (2 * sinTheta);
        const cY = (magnitudeB * a[0] - magnitudeA * b[0]) / (2 * sinTheta);

        return [cX, cY];
    }

    override handlePointerEvent(event: GeoPointerEvent): GeoPointerEvent {
        if (event.eventType == 'pointerdown') {
            if (!this.shouldHandleClick(event)) return event;
        }

        const ctrl = getFocusDocCtrl(this.editor, event.viewport.id);
        if (!ctrl?.state) return event;

        if (event.eventType == 'pointermove') {
            this.pointerMoveCachingReflowSync.handleEvent(event, event =>
                handleIfPointerNotInError(this, () => this.doTrySelection(event, ctrl))
            );
        } else {
            this.doTrySelection(event, ctrl);
        }

        event.continue = false;
        event.nativeEvent.preventDefault();

        return event;
    }

    private doTrySelection(event: GeoPointerEvent, ctrl: GeoDocCtrl) {
        if (this.selectionStep === 'angle') {
            this.handleAngleSelection(event, ctrl);
        } else if (this.selectionStep === 'endpoint' && this.endSelector) {
            this.endSelector.trySelect(event, ctrl);
        }
        this.pQ.flush(ctrl);
    }

    /**
     * Handle angle selection manually
     */
    private handleAngleSelection(event: GeoPointerEvent, ctrl: GeoDocCtrl) {
        if (event.eventType === 'pointerup') {
            const { hitEl } = this.posAndCtrl(event);
            if (hitEl && hitEl.type === 'RenderAngle') {
                this.selectedAngle = hitEl as RenderAngle;
                this.bisectorVector = this.calculateBiSectorVectorOfAngle(
                    this.selectedAngle.vectorStart,
                    this.selectedAngle.vectorEnd
                );

                // Move to endpoint selection step
                this.selectionStep = 'endpoint';
                this.createEndpointSelector(ctrl);
            }
        }
    }

    /**
     * Create selector for endpoint selection
     */
    private createEndpointSelector(ctrl: GeoDocCtrl) {
        this.endSelector = or(
            [
                vertex({
                    previewQueue: this.pQ,
                    cursor: this.pointerHandler.cursor,
                }),
                vertexOnStroke({
                    selectableStrokeTypes: ['RenderLine', 'RenderLineSegment', 'RenderRay', 'RenderVector'],
                    previewQueue: this.pQ,
                    cursor: this.pointerHandler.cursor,
                }),
                stroke({
                    selectableStrokeTypes: ['RenderLine', 'RenderLineSegment', 'RenderRay', 'RenderVector'],
                    previewQueue: this.pQ,
                    cursor: this.pointerHandler.cursor,
                }),
            ],
            {
                flatten: true,
                onComplete: async (selector, doc) => {
                    const endElement = Array.isArray(selector.selected) ? selector.selected[0] : selector.selected;
                    await this.performConstruction(doc, endElement as RenderVertex | RenderLine);
                },
            }
        );

        // If no endpoint is selected after a timeout, create simple bisector
        setTimeout(() => {
            if (this.selectionStep === 'endpoint' && this.selectedAngle) {
                this.performConstruction(ctrl);
            }
        }, 5000); // 5 second timeout
    }

    /**
     * Performs the bisector construction based on selected elements
     */
    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async performConstruction(ctrl: GeoDocCtrl, endElement?: RenderVertex | RenderLine) {
        if (!this.selectedAngle || !this.bisectorVector) return;

        try {
            const angleName = this.selectedAngle.name;
            let construction: GeoElConstructionRequest;

            if (!endElement) {
                // Simple bisector line
                construction = this.buildBisectorConstruction(angleName);
            } else if (endElement.type === 'RenderVertex') {
                // Bisector segment to point
                const pointEnd = endElement as RenderVertex;

                // Request name for the point if it's a preview point
                if (pointEnd.relIndex < 0) {
                    const nt = this.toolbar.getTool('NamingElementTool') as NamingElementTool;
                    const inputPointNames = (
                        await requestElementNames(ctrl, nt, [
                            {
                                objName: 'Tên điểm cuối phân giác',
                                originElement: [pointEnd],
                                pickName: pickPointName,
                                namesToAvoid: [],
                            },
                        ])
                    )[0];

                    if (!inputPointNames.length) {
                        this.resetState();
                        return;
                    }
                    pointEnd.name = inputPointNames[0];
                }

                // Calculate scaling factor for segment
                const anglePoint = ctrl.rendererCtrl.elementAt(this.selectedAngle.anglePointIdx) as RenderVertex;
                const k = this.calculateScalingFactor(this.bisectorVector, anglePoint.coords, pointEnd.coords);

                construction = this.buildBisectorSegmentConstruction(pointEnd.name, angleName, k);
            } else {
                // Bisector segment with intersection line
                const intersectLine = endElement as RenderLine;
                construction = this.buildBisectorSegmentAndIntersectionLineConstruction(
                    angleName,
                    intersectLine.name,
                    intersectLine.elType
                );
            }

            this.resetState();

            await ctrl.editor.awarenessFeature.useAwareness(
                ctrl.viewport.id,
                'Đang tạo đường phân giác',
                buildDocumentAwarenessCmdOption(ctrl.editor.awarenessConstructId, ctrl),
                async () => {
                    const constructions: ConstructionRequest[] = [];

                    // Add point construction if needed
                    if (endElement?.type === 'RenderVertex' && endElement.relIndex < 0) {
                        const vertex = endElement as RenderVertex;
                        constructions.push({
                            construction: buildPointConstruction(vertex.name, {
                                x: vertex.coords[0],
                                y: vertex.coords[1],
                            }),
                        });
                    }

                    constructions.push({ construction });

                    const constructResponse = await constructExec(() =>
                        this.editor.geoGateway.construct(ctrl.state.globalId, constructions)
                    );

                    await syncRenderCommands(constructResponse.render, ctrl);
                    addHistoryItemFromConstructionResponse(ctrl, constructResponse);
                }
            );
        } catch (error) {
            console.error('Error in bisector construction:', error);
            this.resetState();
            throw error;
        }
    }

    /**
     * Calculate scaling factor for bisector segment
     */
    private calculateScalingFactor(bisectorVector: number[], startPoint: number[], endPoint: number[]): number {
        const dx = endPoint[0] - startPoint[0];
        const dy = endPoint[1] - startPoint[1];
        const distance = Math.sqrt(dx * dx + dy * dy);

        const vectorMagnitude = Math.sqrt(
            bisectorVector[0] * bisectorVector[0] + bisectorVector[1] * bisectorVector[1]
        );

        return distance / vectorMagnitude;
    }

    // Construction methods - keeping the original selector actions

    private buildBisectorConstruction(angleName: string): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest('LineVi/BisectorOfAngleEC', 'LineVi', 'BisectorAngle');

        construction.paramSpecs = [
            {
                indexInCG: 0,
                paramDefId: 'anAngle',
                optional: false,
                tplStrLangId: 'tpl-BisectorOfAngle',
                params: {
                    name: {
                        type: 'singleValue',
                        value: angleName,
                    },
                },
            },
        ];

        return construction;
    }

    private buildBisectorSegmentConstruction(name: string, angleName: string, k: number): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest('LineVi/BisectorOfAngleEC', 'LineVi', 'BisectorAngleSegment');
        construction.name = name;

        construction.paramSpecs = [
            {
                indexInCG: 0,
                paramDefId: 'anAngle',
                optional: false,
                tplStrLangId: 'tpl-BisectorOfAngle',
                params: {
                    name: {
                        type: 'singleValue',
                        value: angleName,
                    },
                },
            },
            {
                indexInCG: 1,
                paramDefId: 'aValue',
                optional: false,
                tplStrLangId: 'tpl-2DPoint',
                params: {
                    value: {
                        type: 'singleValue',
                        value: k,
                    },
                },
            },
        ];

        return construction;
    }

    private buildBisectorSegmentAndIntersectionLineConstruction(
        angleName: string,
        intersectionLineName: string,
        intersectionLineType: string
    ): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest(
            'LineVi/BisectorOfAngleEC',
            'LineVi',
            'BisectorAngleSegmentWithIntersectionLine'
        );

        construction.paramSpecs = [
            {
                indexInCG: 0,
                paramDefId: 'anAngle',
                optional: false,
                tplStrLangId: 'tpl-BisectorOfAngle',
                params: {
                    name: {
                        type: 'singleValue',
                        value: angleName,
                    },
                },
            },
            {
                indexInCG: 1,
                paramDefId: 'aLine',
                optional: false,
                tplStrLangId: 'tpl-IntersectionLine',
                params: {
                    name: {
                        type: 'singleValue',
                        value: intersectionLineName,
                    },
                },
                dataTypes: {
                    name: intersectionLineType,
                },
            },
        ];

        return construction;
    }
}
